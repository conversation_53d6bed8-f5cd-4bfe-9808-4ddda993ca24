image: python:3.13-alpine

variables:
    PROJECTNAME: $CI_PROJECT_NAME
    RELEASETAG: $CI_COMMIT_SHORT_SHA

stages:
    - test
    - release
test:
  stage: test
  script:
    - pip install coverage
    - pip install -r requirements.txt
    - python -m coverage run --data-file=.coverage --source=phases,tests -m unittest discover -v
    - python -m coverage report --data-file=.coverage
    - python -m coverage xml --data-file=.coverage -o cov/coverage.xml
    - python -m coverage html --data-file=.coverage -d cov/htmlcov
  coverage: '/TOTAL.*\s+(\d+\%)/'
  artifacts:
    paths:
      - :./cov/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: cov/coverage.xml

pypi:
    stage: release
    cache: {}
    variables:
        APP_VERSION: $CI_COMMIT_TAG
    script:
        - sed -i "s/v0.0.0/${APP_VERSION}/g" phases/__init__.py
        - sed -i "s/v0.0.0/${APP_VERSION}/g" setup.py
        - pip install -U -r requirements.txt
        - pip install -U twine
        - python setup.py sdist
        - twine upload dist/*
    rules:
        - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+(\.\d+)?(-\S*)?$/'

make-badge:
    before_script:
        - apk update
        - apk add jq curl
    stage: release
    variables:
        APP_VERSION: $CI_COMMIT_TAG
    script:
        - |
            if echo "$APP_VERSION" | grep -Eq '^v[0-9]+\.[0-9]+\.[0-9]+$'; then
              badgename="version"
            else
              badgename="pre--release"
              APP_VERSION=$(echo "$APP_VERSION" | sed 's/-/--/g')
            fi
        - version_badge_id=$(curl --header "PRIVATE-TOKEN:${BADGETOKEN}" "https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges" | jq -c "map(select(.name == \"$badgename\"))[0].id")
        - curl --request PUT --header "PRIVATE-TOKEN:${BADGETOKEN}" --data "image_url=https://img.shields.io/badge/$badgename-${APP_VERSION}-blue" "https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${version_badge_id}"
    rules:
        - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+(\.\d+)?(-\S*)?$/'