[{"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "yaml", "kind": 6, "isExtraImport": true, "importPath": "yaml", "description": "yaml", "detail": "yaml", "documentation": {}}, {"label": "py<PERSON><PERSON>es", "kind": 6, "isExtraImport": true, "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "pdict", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "classLogger", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Project", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "pdict", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "CSVLogger", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Data", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Project", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "pdict", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Project", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "LogLevel", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Phase", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Project", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "pdict", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "pdict", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "CSVLogger", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "pdict", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Data", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Phase", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Project", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "pdict", "importPath": "py<PERSON><PERSON>es", "description": "py<PERSON><PERSON>es", "isExtraImport": true, "detail": "py<PERSON><PERSON>es", "documentation": {}}, {"label": "Base", "importPath": "phases.commands.Base", "description": "phases.commands.Base", "isExtraImport": true, "detail": "phases.commands.Base", "documentation": {}}, {"label": "Base", "importPath": "phases.commands.Base", "description": "phases.commands.Base", "isExtraImport": true, "detail": "phases.commands.Base", "documentation": {}}, {"label": "Base", "importPath": "phases.commands.Base", "description": "phases.commands.Base", "isExtraImport": true, "detail": "phases.commands.Base", "documentation": {}}, {"label": "Base", "importPath": "phases.commands.Base", "description": "phases.commands.Base", "isExtraImport": true, "detail": "phases.commands.Base", "documentation": {}}, {"label": "Base", "importPath": "phases.commands.Base", "description": "phases.commands.Base", "isExtraImport": true, "detail": "phases.commands.Base", "documentation": {}}, {"label": "Misconfigured", "importPath": "phases.commands.Base", "description": "phases.commands.Base", "isExtraImport": true, "detail": "phases.commands.Base", "documentation": {}}, {"label": "Base", "importPath": "phases.commands.Base", "description": "phases.commands.Base", "isExtraImport": true, "detail": "phases.commands.Base", "documentation": {}}, {"label": "copy_tree", "importPath": "distutils.dir_util", "description": "distutils.dir_util", "isExtraImport": true, "detail": "distutils.dir_util", "documentation": {}}, {"label": "copy_tree", "importPath": "distutils.dir_util", "description": "distutils.dir_util", "isExtraImport": true, "detail": "distutils.dir_util", "documentation": {}}, {"label": "random", "kind": 6, "isExtraImport": true, "importPath": "random", "description": "random", "detail": "random", "documentation": {}}, {"label": "floor", "importPath": "math", "description": "math", "isExtraImport": true, "detail": "math", "documentation": {}}, {"label": "Run", "importPath": "phases.commands.run", "description": "phases.commands.run", "isExtraImport": true, "detail": "phases.commands.run", "documentation": {}}, {"label": "Run", "importPath": "phases.commands.run", "description": "phases.commands.run", "isExtraImport": true, "detail": "phases.commands.run", "documentation": {}}, {"label": "Run", "importPath": "phases.commands.run", "description": "phases.commands.run", "isExtraImport": true, "detail": "phases.commands.run", "documentation": {}}, {"label": "Run", "importPath": "phases.commands.run", "description": "phases.commands.run", "isExtraImport": true, "detail": "phases.commands.run", "documentation": {}}, {"label": "importlib", "kind": 6, "isExtraImport": true, "importPath": "importlib", "description": "importlib", "detail": "importlib", "documentation": {}}, {"label": "copy_file", "importPath": "distutils.file_util", "description": "distutils.file_util", "isExtraImport": true, "detail": "distutils.file_util", "documentation": {}}, {"label": "unittest", "kind": 6, "isExtraImport": true, "importPath": "unittest", "description": "unittest", "detail": "unittest", "documentation": {}}, {"label": "TextTestRunner", "importPath": "unittest", "description": "unittest", "isExtraImport": true, "detail": "unittest", "documentation": {}}, {"label": "mock", "importPath": "unittest", "description": "unittest", "isExtraImport": true, "detail": "unittest", "documentation": {}}, {"label": "TestCase", "importPath": "unittest", "description": "unittest", "isExtraImport": true, "detail": "unittest", "documentation": {}}, {"label": "TestCase", "importPath": "unittest", "description": "unittest", "isExtraImport": true, "detail": "unittest", "documentation": {}}, {"label": "TestSuite", "importPath": "unittest", "description": "unittest", "isExtraImport": true, "detail": "unittest", "documentation": {}}, {"label": "TextTestRunner", "importPath": "unittest", "description": "unittest", "isExtraImport": true, "detail": "unittest", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "unittest.loader", "description": "unittest.loader", "isExtraImport": true, "detail": "unittest.loader", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "importPath": "unittest.loader", "description": "unittest.loader", "isExtraImport": true, "detail": "unittest.loader", "documentation": {}}, {"label": "TestSuite", "importPath": "unittest.suite", "description": "unittest.suite", "isExtraImport": true, "detail": "unittest.suite", "documentation": {}}, {"label": "TestCase", "importPath": "pyPhases.test", "description": "pyPhases.test", "isExtraImport": true, "detail": "pyPhases.test", "documentation": {}}, {"label": "TestCaseIntegration", "importPath": "pyPhases.test", "description": "pyPhases.test", "isExtraImport": true, "detail": "pyPhases.test", "documentation": {}}, {"label": "mockLogger", "importPath": "pyPhases.test", "description": "pyPhases.test", "isExtraImport": true, "detail": "pyPhases.test", "documentation": {}}, {"label": "mockLogger", "importPath": "pyPhases.test", "description": "pyPhases.test", "isExtraImport": true, "detail": "pyPhases.test", "documentation": {}}, {"label": "TestCase", "importPath": "pyPhases.test", "description": "pyPhases.test", "isExtraImport": true, "detail": "pyPhases.test", "documentation": {}}, {"label": "TestCaseIntegration", "importPath": "pyPhases.test", "description": "pyPhases.test", "isExtraImport": true, "detail": "pyPhases.test", "documentation": {}}, {"label": "getmembers", "importPath": "inspect", "description": "inspect", "isExtraImport": true, "detail": "inspect", "documentation": {}}, {"label": "isclass", "importPath": "inspect", "description": "inspect", "isExtraImport": true, "detail": "inspect", "documentation": {}}, {"label": "docopt", "importPath": "docopt", "description": "docopt", "isExtraImport": true, "detail": "docopt", "documentation": {}}, {"label": "phases", "kind": 6, "isExtraImport": true, "importPath": "phases", "description": "phases", "detail": "phases", "documentation": {}}, {"label": "__version__", "importPath": "phases", "description": "phases", "isExtraImport": true, "detail": "phases", "documentation": {}}, {"label": "phases.commands", "kind": 6, "isExtraImport": true, "importPath": "phases.commands", "description": "phases.commands", "detail": "phases.commands", "documentation": {}}, {"label": "patch", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "MagicMock", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "call", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "mock_open", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "patch", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "MagicMock", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "call", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "MagicMock", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "MagicMock", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "call", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "patch", "importPath": "unittest.mock", "description": "unittest.mock", "isExtraImport": true, "detail": "unittest.mock", "documentation": {}}, {"label": "main", "importPath": "phases.cli", "description": "phases.cli", "isExtraImport": true, "detail": "phases.cli", "documentation": {}}, {"label": "Create", "importPath": "phases.commands.create", "description": "phases.commands.create", "isExtraImport": true, "detail": "phases.commands.create", "documentation": {}}, {"label": "Create", "importPath": "phases.commands.create", "description": "phases.commands.create", "isExtraImport": true, "detail": "phases.commands.create", "documentation": {}}, {"label": "copyfile", "importPath": "shutil", "description": "shutil", "isExtraImport": true, "detail": "shutil", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "importPath": "phases.commands.gridrun", "description": "phases.commands.gridrun", "isExtraImport": true, "detail": "phases.commands.gridrun", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "importPath": "phases.commands.gridrun", "description": "phases.commands.gridrun", "isExtraImport": true, "detail": "phases.commands.gridrun", "documentation": {}}, {"label": "TestCreate", "importPath": "tests.acceptance.test_create", "description": "tests.acceptance.test_create", "isExtraImport": true, "detail": "tests.acceptance.test_create", "documentation": {}}, {"label": "Test", "importPath": "phases.commands.test", "description": "phases.commands.test", "isExtraImport": true, "detail": "phases.commands.test", "documentation": {}}, {"label": "setuptools", "kind": 6, "isExtraImport": true, "importPath": "setuptools", "description": "setuptools", "detail": "setuptools", "documentation": {}}, {"label": "Misconfigured", "kind": 6, "importPath": "phases.commands.Base", "description": "phases.commands.Base", "peekOfCode": "class Misconfigured(Exception):\n    pass\n@classLogger\nclass Base(object):\n    outputDir = None\n    \"\"\"A base command.\"\"\"\n    def __init__(self, options, *args, **kwargs):\n        self.options = options\n        self.args = args\n        self.kwargs = kwargs", "detail": "phases.commands.Base", "documentation": {}}, {"label": "Base", "kind": 6, "importPath": "phases.commands.Base", "description": "phases.commands.Base", "peekOfCode": "class Base(object):\n    outputDir = None\n    \"\"\"A base command.\"\"\"\n    def __init__(self, options, *args, **kwargs):\n        self.options = options\n        self.args = args\n        self.kwargs = kwargs\n        self.config = {}\n        self.projectFileName = \"project.yaml\"\n        self.projectGridFile = None", "detail": "phases.commands.Base", "documentation": {}}, {"label": "Create", "kind": 6, "importPath": "phases.commands.create", "description": "phases.commands.create", "peekOfCode": "class Create(Base):\n    \"\"\"create a Phase-Project\"\"\"\n    templateDir = \"generate-template/\"\n    staticFilesDir = \"static-template/\"\n    packagePath = os.path.dirname(sys.modules[\"phases\"].__file__)\n    forceWrite = False\n    def parseRunOptions(self):\n        if self.options[\"-o\"]:\n            self.outputDir = self.options[\"-o\"]\n            self.logDebug(\"Set Outputdir: %s\" % (self.outputDir))", "detail": "phases.commands.create", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "kind": 6, "importPath": "phases.commands.gridrun", "description": "phases.commands.gridrun", "peekOfCode": "class Gridrun(Run):\n    \"\"\"run a grid search\"\"\"\n    def __init__(self, options, *args, **kwargs):\n        super().__init__(options, *args, **kwargs)\n        self.resume = True\n        self.writer = None\n        self.runSettings = None\n        self.csvLogFile = \"output.csv\"\n    def parseRunOptions(self):\n        super().parseRunOptions()", "detail": "phases.commands.gridrun", "documentation": {}}, {"label": "Run", "kind": 6, "importPath": "phases.commands.run", "description": "phases.commands.run", "peekOfCode": "class Run(Base):\n    \"\"\"create a Phase-Project\"\"\"\n    config = None\n    projectFileName = \"project.yaml\"\n    packagePath = os.path.dirname(sys.modules[\"phases\"].__file__)\n    forceWrite = False\n    debug = False\n    phaseName = None\n    def run(self):\n        self.beforeRun()", "detail": "phases.commands.run", "documentation": {}}, {"label": "Savedata", "kind": 6, "importPath": "phases.commands.savedata", "description": "phases.commands.savedata", "peekOfCode": "class Savedata(Run):\n    \"\"\"create a Phase-Project\"\"\"\n    def parseRunOptions(self):\n        super().parseRunOptions()\n        self.dataname = self.options[\"<dataname>\"]\n        self.datafile = self.options[\"<datafile>\"]\n        self.datadir = \".\" if self.options[\"<datadir>\"] is None else self.options[\"<datadir>\"]\n    def run(self):\n        self.beforeRun()\n        self.prepareConfig()", "detail": "phases.commands.savedata", "documentation": {}}, {"label": "Test", "kind": 6, "importPath": "phases.commands.test", "description": "phases.commands.test", "peekOfCode": "class Test(Run):\n    \"\"\"test a Phase-Project by wrapping TestCases from pyPhase with the project and a configfile specified in project.test.yaml\"\"\"\n    def __init__(self, options, *args, **kwargs):\n        super().__init__(options, *args, **kwargs)\n        self.testDir = \"tests\"\n        self.failFast = False\n        self.testPattern = \"test*.py\"\n    def parseRunOptions(self):\n        super().parseRunOptions()\n        if self.options[\"<testdir>\"]:", "detail": "phases.commands.test", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "phases.cli", "description": "phases.cli", "peekOfCode": "def main():\n    \"\"\"Main CLI entrypoint.\"\"\"\n    options = docopt(__doc__, version=VERSION)\n    if options[\"-v\"]:\n        Logger.verboseLevel = LogLevel.DEBUG\n    Logger.log(\n        \"Phases %s with pyPhases %s (Log level: %s)\" % (VERSION, pyPhases.__version__, Logger.verboseLevel),\n        \"phases\",\n    )\n    # Here we'll try to dynamically match the command the user is trying to run", "detail": "phases.cli", "documentation": {}}, {"label": "TestPhasesModule", "kind": 6, "importPath": "tests.acceptance.test_cli", "description": "tests.acceptance.test_cli", "peekOfCode": "class TestPhasesModule(unittest.TestCase):\n    @patch(\"phases.commands.run.Run.run\")\n    @patch(\"phases.commands.Base.Base.run\")\n    @patch.object(sys, \"exit\")\n    @mockLogger\n    def test_version(self, mockLogger, exitmock, basemock, runmock):\n        # Arrange\n        sys.argv = [\"phases\", \"--version\"]\n        expected_output = \"Phases %s with pyPhases %s (Log level: %s)\" % (\n            phases.__version__,", "detail": "tests.acceptance.test_cli", "documentation": {}}, {"label": "TestCreate", "kind": 6, "importPath": "tests.acceptance.test_create", "description": "tests.acceptance.test_create", "peekOfCode": "class TestCreate(unittest.TestCase):\n    def test_parseRunOptions_default(self):\n        \"\"\"Test that the options are parsed correctly\"\"\"\n        create = Create({})\n        self.assertEqual(create.forceWrite, False)\n    def test_parseRunOptions(self):\n        \"\"\"Test that the options are parsed correctly\"\"\"\n        create = Create({})\n        create.options = {\n            \"-o\": \"o\",", "detail": "tests.acceptance.test_create", "documentation": {}}, {"label": "MockPhase", "kind": 6, "importPath": "tests.acceptance.test_grid", "description": "tests.acceptance.test_grid", "peekOfCode": "class MockPhase(Phase):\n    index = 0\n    def main(self):\n        self.project.gridOutput = {\"runIndex\": MockPhase.index}\n        MockPhase.index += 1\nclass TestGrid(unittest.TestCase):\n    fullGrid = [\n        ([\"p0\"], [0, 1]),\n        ([\"p1\"], [2, 3]),\n        ([\"p2\"], [4, 5, 6]),", "detail": "tests.acceptance.test_grid", "documentation": {}}, {"label": "TestGrid", "kind": 6, "importPath": "tests.acceptance.test_grid", "description": "tests.acceptance.test_grid", "peekOfCode": "class TestGrid(unittest.TestCase):\n    fullGrid = [\n        ([\"p0\"], [0, 1]),\n        ([\"p1\"], [2, 3]),\n        ([\"p2\"], [4, 5, 6]),\n        ([\"p3\"], [7, 8]),\n        ([\"p4\"], [9]),\n        ([\"p5\", \"deep\"], [10, 11, 12]),\n    ]\n    def runFullGrid(self, random=False, resume=False):", "detail": "tests.acceptance.test_grid", "documentation": {}}, {"label": "TestCompleteRun", "kind": 6, "importPath": "tests.acceptance.test_run", "description": "tests.acceptance.test_run", "peekOfCode": "class TestCompleteRun(unittest.TestCase):\n    \"\"\"create a project and run it\"\"\"\n    projectFolder = \"tests/data-gen\"\n    configFile = \"tests/data/min.yaml\"\n    pass\n    # @classmethod\n    # def setUpClass(cls):\n    #     testrun = Run({})\n    #     path = Path(cls.projectFolder)\n    #     if path.exists():", "detail": "tests.acceptance.test_run", "documentation": {}}, {"label": "TestBase", "kind": 6, "importPath": "tests.unit.test_base", "description": "tests.unit.test_base", "peekOfCode": "class TestBase(unittest.TestCase):\n    def test_run(self):\n        b = Base({})\n        self.assertRaises(NotImplementedError, b.run)\n    def test_overwriteConfigByEnviroment(self):\n        b = Base({})\n        config = {\"foo\": \"bar\", \"foobar\": {\"foo2\": \"bar2\"}}\n        os.environ[\"PHASE_CONFIG_foo\"] = \"Test1\"\n        os.environ[\"PHASE_CONFIG_foo2\"] = \"doesnotexist\"\n        os.environ[\"PHASE_CONFIG_foobar_foo2\"] = \"Test2\"", "detail": "tests.unit.test_base", "documentation": {}}, {"label": "TestConfig", "kind": 6, "importPath": "tests.unit.test_config", "description": "tests.unit.test_config", "peekOfCode": "class TestConfig(unittest.TestCase):\n    projectFolder = \"tests/data-gen\"\n    configFile = \"tests/data/min.yaml\"\n    configFileFull = \"tests/importFull.yaml\"\n    def testLoadConfigMain(self):\n        base = Base({})\n        fullConfig = base.loadConfig(self.configFile, root=True)\n        projectConfig = fullConfig[\"config\"]\n        assert projectConfig[\"before\"] == \"before\"\n        assert projectConfig[\"test\"] == \"main\"", "detail": "tests.unit.test_config", "documentation": {}}, {"label": "TestGridRun", "kind": 6, "importPath": "tests.unit.test_gridrun", "description": "tests.unit.test_gridrun", "peekOfCode": "class TestGridRun(unittest.TestCase):\n    def test_parseRunOptions_defaults(self):\n        gridrun = Gridrun({})\n        self.assertEqual(gridrun.projectGridFile, None)\n        self.assertEqual(gridrun.csvLogFile, \"output.csv\")\n        self.assertEqual(gridrun.resume, True)\n    def test_parseRunOptions(self):\n        gridrun = Gridrun({})\n        gridrun.options = {\n            \"<gridfile>\": \"g\",", "detail": "tests.unit.test_gridrun", "documentation": {}}, {"label": "TestRun", "kind": 6, "importPath": "tests.unit.test_run", "description": "tests.unit.test_run", "peekOfCode": "class TestRun(TestCase):\n    def getDefaultConfig(self):\n        return pdict(\n            {\n                \"name\": \"test\",\n                \"namespace\": \"testns\",\n                \"phases\": [],\n                \"config\": {},\n                \"exporter\": [],\n                \"data\": [],", "detail": "tests.unit.test_run", "documentation": {}}, {"label": "TestTest", "kind": 6, "importPath": "tests.unit.test_test", "description": "tests.unit.test_test", "peekOfCode": "class TestTest(TestCase):\n    def test_parseRunOptions_default(self):\n        \"\"\"Test that the run options are parsed correctly\"\"\"\n        test = Test({})\n        self.assertEqual(test.testDir, \"tests\")\n        self.assertEqual(test.testPattern, \"test*.py\")\n        self.assertEqual(test.failFast, False)\n    def test_parseRunOptions(self):\n        \"\"\"Test that the run options are parsed correctly\"\"\"\n        test = Test({})", "detail": "tests.unit.test_test", "documentation": {}}]